package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.util.CarregarCertificados;
import com.br.sasw.esocial_novo.util.Certificado;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HttpProcessor;
import org.apache.http.protocol.HttpProcessorBuilder;
import org.apache.http.protocol.RequestTargetHost;
import org.apache.http.protocol.RequestUserAgent;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLContext;
import java.io.FileInputStream;
import java.security.KeyStore;


@Service
public class HttpClientService {

    public CloseableHttpClient createHttpClientForEmpresa(String empresa) throws Exception {
        Certificado certificado = CarregarCertificados.buscaCertificadoPorChaveEmpresa(empresa);
        
        if (certificado == null) {
            throw new IllegalArgumentException("Certificado não encontrado para a empresa: " + empresa);
        }

        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(new FileInputStream(certificado.getLocal()), certificado.getSenha().toCharArray());

        SSLContext sslContext = SSLContextBuilder.create()
                .loadKeyMaterial(keyStore, certificado.getSenha().toCharArray())
                .loadTrustMaterial(null, (chain, authType) -> true)
                .build();

        HttpProcessor httpProcessor = HttpProcessorBuilder.create()
                .add(new RequestTargetHost())
                .add(new RequestUserAgent("Spring WS"))
                .build();

        return HttpClients.custom()
                .setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext))
                .setHttpProcessor(httpProcessor)
                .disableContentCompression()
                .build();
    }
}
