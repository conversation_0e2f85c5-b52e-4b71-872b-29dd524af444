package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.util.CarregarCertificados;
import com.br.sasw.esocial_novo.util.Certificado;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class HttpClientServiceTest {

    @Autowired
    private HttpClientService httpClientService;

    @Test
    void testCreateHttpClientForEmpresaFederal() throws Exception {
        // Verifica se consegue criar HttpClient para empresa FEDERAL
        CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa("FEDERAL");
        assertNotNull(httpClient);
        httpClient.close();
    }

    @Test
    void testCreateHttpClientForEmpresaInvioseg() throws Exception {
        // Verifica se consegue criar HttpClient para empresa INVIOSEG
        CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa("INVIOSEG");
        assertNotNull(httpClient);
        httpClient.close();
    }

    @Test
    void testCreateHttpClientForEmpresaInexistente() {
        // Verifica se lança exceção para empresa inexistente
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            httpClientService.createHttpClientForEmpresa("EMPRESA_INEXISTENTE");
        });
        
        assertTrue(exception.getMessage().contains("Certificado não encontrado para a empresa"));
    }

    @Test
    void testCarregarCertificadosRetornaCertificadoCorreto() {
        // Testa se o CarregarCertificados está retornando os certificados corretos
        Certificado certificadoFederal = CarregarCertificados.buscaCertificadoPorChaveEmpresa("FEDERAL");
        assertNotNull(certificadoFederal);
        assertEquals("C:\\certificados-chaves\\FEDERAL\\FEDERAL_GOIANIA.pfx", certificadoFederal.getLocal());
        assertEquals("Federallcom13", certificadoFederal.getSenha());

        Certificado certificadoInvioseg = CarregarCertificados.buscaCertificadoPorChaveEmpresa("INVIOSEG");
        assertNotNull(certificadoInvioseg);
        assertEquals("C:\\certificados-chaves\\INVIOSEG\\INVIOSEG.pfx", certificadoInvioseg.getLocal());
        assertEquals("1234", certificadoInvioseg.getSenha());
    }
}
