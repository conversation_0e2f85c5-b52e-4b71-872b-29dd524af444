package com.br.sasw.esocial_novo.controller;

import com.br.sasw.esocial_novo.esocial.ReinfService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ReinfSenderController {

    private final ReinfService service;

    @PostMapping(path = "/envio-reinf")
    public String send(@RequestBody String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor) {

        return service.send(xml, id, empresa, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor, tpInscTransmissor);
    }
}
