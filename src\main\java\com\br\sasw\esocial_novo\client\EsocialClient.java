package com.br.sasw.esocial_novo.client;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventos;
import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;
import com.br.sasw.esocial_novo.config.SoapConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

@Service
public class EsocialClient extends WebServiceGatewaySupport {

    @Autowired
    private SoapConfig.WebServiceTemplateFactory webServiceTemplateFactory;

    public EnviarLoteEventosResponse enviarLoteEventos(String xmlLoteEventos, String empresa) {
        try {
            WebServiceTemplate empresaWebServiceTemplate = webServiceTemplateFactory.createForEmpresa(empresa);

            EnviarLoteEventos request = new EnviarLoteEventos();

            org.w3c.dom.Element xmlElement = criarElementoXml(xmlLoteEventos);
            request.setLoteEventos(new EnviarLoteEventos.LoteEventos());
            request.getLoteEventos().setAny(xmlElement);

            String soapAction = "http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/ServicoEnviarLoteEventos/EnviarLoteEventos";

            return (EnviarLoteEventosResponse) empresaWebServiceTemplate
                    .marshalSendAndReceive(
                            "https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc",
                            request,
                            new SoapActionCallback(soapAction)
                    );
        } catch (Exception e) {
            throw new RuntimeException("Erro ao enviar lote de eventos para empresa: " + empresa, e);
        }
    }
    
    private org.w3c.dom.Element criarElementoXml(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));
            return document.getDocumentElement();
        } catch (Exception e) {
            throw new RuntimeException("Erro ao criar elemento XML", e);
        }
    }
}