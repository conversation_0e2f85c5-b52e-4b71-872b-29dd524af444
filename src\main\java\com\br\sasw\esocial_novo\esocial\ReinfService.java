package com.br.sasw.esocial_novo.esocial;

import com.br.sasw.esocial_novo.service.HttpClientService;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReinfService {

    private final HttpClientService httpClientService;

    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        // Para REINF, assinamos o evento individual e depois construímos o envelope
        String signedXml = XmlSigner.sign(xml, empresa);
        String eventoCompleto = buildEvent(signedXml, id, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor,tpInscTransmissor);

        return eventoCompleto;
    }

    private String buildEvent(String xmlEventoAssinado, String id, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){
        // Extrair apenas o conteúdo interno do XML assinado, removendo o elemento Reinf externo se existir
        String conteudoEvento = extractEventContent(xmlEventoAssinado);

        StringBuilder builder = new StringBuilder();
        builder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        builder.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        builder.append("<envioLoteEventos>");
        builder.append("<ideContribuinte>");
        builder.append("<tpInsc>${tpInscEmpregador}</tpInsc>");
        builder.append("<nrInsc>${nrInscEmpregador}</nrInsc>");
        builder.append("</ideContribuinte>");
        builder.append("<eventos>");
        builder.append("<evento Id=\"${id}\">");
        builder.append(conteudoEvento);
        builder.append("</evento>");
        builder.append("</eventos>");
        builder.append("</envioLoteEventos>");
        builder.append("</Reinf>");

        String evento = builder.toString();

        evento = evento.replace("${id}", id);
        evento = evento.replace("${nrInscEmpregador}", nrInscEmpregador);
        evento = evento.replace("${tpInscEmpregador}", tpInscEmpregador);
        return evento;
    }

    private String extractEventContent(String xmlAssinado) {
        // Se o XML contém um elemento Reinf externo, extrair apenas o conteúdo interno
        if (xmlAssinado.contains("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/")) {
            // Encontrar o início do conteúdo após a tag Reinf
            int startIndex = xmlAssinado.indexOf(">", xmlAssinado.indexOf("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/")) + 1;
            // Encontrar o fim antes da tag de fechamento </Reinf>
            int endIndex = xmlAssinado.lastIndexOf("</Reinf>");
            if (startIndex > 0 && endIndex > startIndex) {
                return xmlAssinado.substring(startIndex, endIndex).trim();
            }
        }
        // Se não encontrar o padrão esperado, retornar o XML original
        return xmlAssinado;
    }
}
