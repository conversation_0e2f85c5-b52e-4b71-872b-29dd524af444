package com.br.sasw.esocial_novo.esocial;

import com.br.sasw.esocial_novo.service.HttpClientService;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReinfService {

    private final HttpClientService httpClientService;

    /**
     * Processa e envia um evento REINF versão 2.1.2
     *
     * Estratégia final baseada na documentação oficial:
     * 1. Assinar o evento individual (obrigatório)
     * 2. Construir envelope de lote assíncrono com evento assinado
     * 3. NÃO assinar o envelope (apenas o evento é assinado)
     */
    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        // 1. Assinar o evento individual (obrigatório conforme documentação)
        String eventoAssinado = XmlSigner.sign(xml, empresa);

        // 2. Construir envelope de lote assíncrono com evento assinado completo
        String envelopeLote = buildEnvelopeLoteAssincrono(eventoAssinado, id, nrInscEmpregador, tpInscEmpregador);

        return envelopeLote;
    }





    /**
     * Constrói o envelope de lote assíncrono conforme documentação REINF v2.1
     * O evento já vem assinado e é inserido completo no envelope
     */
    private String buildEnvelopeLoteAssincrono(String eventoAssinado, String id, String nrInscEmpregador, String tpInscEmpregador) {
        StringBuilder envelope = new StringBuilder();

        envelope.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        envelope.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        envelope.append("<envioLoteEventos>");
        envelope.append("<ideContribuinte>");
        envelope.append("<tpInsc>").append(tpInscEmpregador).append("</tpInsc>");
        envelope.append("<nrInsc>").append(nrInscEmpregador).append("</nrInsc>");
        envelope.append("</ideContribuinte>");
        envelope.append("<eventos>");
        envelope.append("<evento Id=\"").append(id).append("\">");
        envelope.append(eventoAssinado);
        envelope.append("</evento>");
        envelope.append("</eventos>");
        envelope.append("</envioLoteEventos>");
        envelope.append("</Reinf>");

        return envelope.toString();
    }
}
