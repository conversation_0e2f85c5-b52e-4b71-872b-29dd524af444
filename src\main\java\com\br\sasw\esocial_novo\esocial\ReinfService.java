package com.br.sasw.esocial_novo.esocial;

import com.br.sasw.esocial_novo.service.HttpClientService;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReinfService {

    private final HttpClientService httpClientService;

    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        // Para REINF, construímos o envelope sem assinatura e depois assinamos o envelope completo
        String eventoCompleto = buildEvent(xml, id, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor,tpInscTransmissor);
        String signedXml = XmlSigner.sign(eventoCompleto, empresa);

        return signedXml;
    }

    private String buildEvent(String xmlEvento, String id, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){
        // Extrair apenas o conteúdo do evento, removendo qualquer elemento Reinf externo e assinatura
        String conteudoEvento = extractEventContentOnly(xmlEvento);

        StringBuilder builder = new StringBuilder();
        builder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        builder.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        builder.append("<envioLoteEventos>");
        builder.append("<ideContribuinte>");
        builder.append("<tpInsc>${tpInscEmpregador}</tpInsc>");
        builder.append("<nrInsc>${nrInscEmpregador}</nrInsc>");
        builder.append("</ideContribuinte>");
        builder.append("<eventos>");
        builder.append("<evento Id=\"${id}\">");
        builder.append(conteudoEvento);
        builder.append("</evento>");
        builder.append("</eventos>");
        builder.append("</envioLoteEventos>");
        builder.append("</Reinf>");

        String evento = builder.toString();

        evento = evento.replace("${id}", id);
        evento = evento.replace("${nrInscEmpregador}", nrInscEmpregador);
        evento = evento.replace("${tpInscEmpregador}", tpInscEmpregador);
        return evento;
    }

    private String extractEventContentOnly(String xml) {
        // Remover qualquer elemento Reinf externo e assinatura, mantendo apenas o conteúdo do evento
        String content = xml;

        // Se contém elemento Reinf externo, extrair o conteúdo interno
        if (content.contains("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/")) {
            int startIndex = content.indexOf(">", content.indexOf("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/")) + 1;
            int endIndex = content.lastIndexOf("</Reinf>");
            if (startIndex > 0 && endIndex > startIndex) {
                content = content.substring(startIndex, endIndex).trim();
            }
        }

        // Remover qualquer elemento Signature se existir
        if (content.contains("<Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\">")) {
            int signatureStart = content.indexOf("<Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\">");
            int signatureEnd = content.indexOf("</Signature>") + "</Signature>".length();
            if (signatureStart >= 0 && signatureEnd > signatureStart) {
                content = content.substring(0, signatureStart) + content.substring(signatureEnd);
                content = content.trim();
            }
        }

        return content;
    }
}
