package com.br.sasw.esocial_novo.esocial;

import com.br.sasw.esocial_novo.service.HttpClientService;
import com.br.sasw.esocial_novo.util.XmlSigner;
import com.br.sasw.esocial_novo.util.CarregarCertificados;
import com.br.sasw.esocial_novo.util.Certificado;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.StringWriter;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Collections;

@Service
@RequiredArgsConstructor
public class ReinfService {

    private final HttpClientService httpClientService;

    /**
     * Processa e envia um evento REINF versão 2.1.2
     *
     * Estratégia final baseada na documentação oficial:
     * 1. Assinar o evento individual com referência específica ao ID
     * 2. Construir envelope de lote assíncrono com evento assinado
     * 3. NÃO assinar o envelope (apenas o evento é assinado)
     */
    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        // 1. Assinar o evento individual com referência específica ao ID (obrigatório conforme documentação)
        String eventoAssinado = signReinfEvent(xml, id, empresa);

        // 2. Construir envelope de lote assíncrono com evento assinado completo
        String envelopeLote = buildEnvelopeLoteAssincrono(eventoAssinado, id, nrInscEmpregador, tpInscEmpregador);

        return envelopeLote;
    }





    /**
     * Constrói o envelope de lote assíncrono conforme documentação REINF v2.1
     * Preserva exatamente a assinatura do evento sem modificações
     */
    private String buildEnvelopeLoteAssincrono(String eventoAssinado, String id, String nrInscEmpregador, String tpInscEmpregador) {
        // O XmlSigner já remove a declaração XML (OMIT_XML_DECLARATION = "yes")
        // Então o eventoAssinado já vem sem <?xml...?>

        StringBuilder envelope = new StringBuilder();

        envelope.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        envelope.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        envelope.append("<envioLoteEventos>");
        envelope.append("<ideContribuinte>");
        envelope.append("<tpInsc>").append(tpInscEmpregador).append("</tpInsc>");
        envelope.append("<nrInsc>").append(nrInscEmpregador).append("</nrInsc>");
        envelope.append("</ideContribuinte>");
        envelope.append("<eventos>");
        envelope.append("<evento Id=\"").append(id).append("\">");
        envelope.append(eventoAssinado); // Insere o evento assinado exatamente como retornado pelo XmlSigner
        envelope.append("</evento>");
        envelope.append("</eventos>");
        envelope.append("</envioLoteEventos>");
        envelope.append("</Reinf>");

        return envelope.toString();
    }

    /**
     * Assina um evento REINF com referência específica ao ID do evento
     * Garante que o elemento com ID seja encontrado corretamente
     */
    private String signReinfEvent(String xml, String eventId, String empresa) {
        try {
            Certificado certificate = CarregarCertificados.buscaCertificadoPorChaveEmpresa(empresa);
            String caminhoCertificado = certificate.getLocal();
            String senhaCertificado = certificate.getSenha();
            String alias = null;

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new FileInputStream(caminhoCertificado), senhaCertificado.toCharArray());

            for (var e = keyStore.aliases(); e.hasMoreElements(); ) {
                alias = e.nextElement();
            }

            PrivateKey chavePrivada = (PrivateKey) keyStore.getKey(alias, senhaCertificado.toCharArray());
            X509Certificate certificado = (X509Certificate) keyStore.getCertificate(alias);

            // Carrega o XML e ativa namespace
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document doc = dbf.newDocumentBuilder().parse(new InputSource(new ByteArrayInputStream(xml.getBytes("utf-8"))));

            // Encontra o elemento com o ID especificado e configura como ID attribute
            org.w3c.dom.Element elementWithId = findElementWithId(doc, eventId);
            if (elementWithId == null) {
                throw new RuntimeException("Elemento com ID '" + eventId + "' não encontrado no XML");
            }

            // Configura o atributo id como ID para o resolvedor de referências
            elementWithId.setIdAttribute("id", true);

            // Cria a fábrica de assinatura
            XMLSignatureFactory sigFactory = XMLSignatureFactory.getInstance("DOM");

            // Cria a referência com ID específico do evento (crucial para REINF)
            Reference ref = sigFactory.newReference("#" + eventId,
                    sigFactory.newDigestMethod(DigestMethod.SHA256, null),
                    Arrays.asList(
                            sigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                            sigFactory.newTransform(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null)
                    ),
                    null,
                    null
            );

            // Define as informações da assinatura
            SignedInfo signedInfo = sigFactory.newSignedInfo(
                    sigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
                    sigFactory.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
                    Collections.singletonList(ref)
            );

            // Cria a KeyInfo com o certificado do usuário final (EndCertOnly)
            KeyInfoFactory kif = sigFactory.getKeyInfoFactory();
            X509Data x509Data = kif.newX509Data(Collections.singletonList(certificado));
            KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(x509Data));

            // Define o local da assinatura (pai do nó a ser assinado)
            DOMSignContext dsc = new DOMSignContext(chavePrivada, doc.getDocumentElement());

            // Cria e aplica a assinatura
            XMLSignature signature = sigFactory.newXMLSignature(signedInfo, keyInfo);
            signature.sign(dsc);

            // Salva o resultado
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "no");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "1");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.METHOD, "xml");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.STANDALONE, "no");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "yes");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.toString();
        } catch (Exception e) {
            throw new RuntimeException("Erro ao assinar evento REINF: " + e.getMessage(), e);
        }
    }

    /**
     * Encontra o elemento com o ID especificado no documento XML
     */
    private org.w3c.dom.Element findElementWithId(Document doc, String id) {
        // Busca por elementos que tenham atributo "id" com o valor especificado
        org.w3c.dom.NodeList nodeList = doc.getElementsByTagName("*");
        for (int i = 0; i < nodeList.getLength(); i++) {
            org.w3c.dom.Node node = nodeList.item(i);
            if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
                org.w3c.dom.Element element = (org.w3c.dom.Element) node;
                String elementId = element.getAttribute("id");
                if (id.equals(elementId)) {
                    return element;
                }
            }
        }
        return null;
    }
}
