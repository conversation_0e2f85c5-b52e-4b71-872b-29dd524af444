package com.br.sasw.esocial_novo.esocial;

import com.br.sasw.esocial_novo.service.HttpClientService;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReinfService {

    private final HttpClientService httpClientService;

    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        // Para REINF, assinamos o evento individual e depois construímos o envelope SEM assinatura
        String signedXml = XmlSigner.sign(xml, empresa);
        String eventoCompleto = buildEventWithSignedContent(signedXml, id, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor,tpInscTransmissor);

        return eventoCompleto;
    }

    private String buildEventWithSignedContent(String xmlEventoAssinado, String id, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){
        // Extrair o conteúdo do evento assinado, mantendo a assinatura dentro do evento
        String conteudoEventoAssinado = extractSignedEventContent(xmlEventoAssinado);

        StringBuilder builder = new StringBuilder();
        builder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        builder.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        builder.append("<envioLoteEventos>");
        builder.append("<ideContribuinte>");
        builder.append("<tpInsc>${tpInscEmpregador}</tpInsc>");
        builder.append("<nrInsc>${nrInscEmpregador}</nrInsc>");
        builder.append("</ideContribuinte>");
        builder.append("<eventos>");
        builder.append("<evento Id=\"${id}\">");
        builder.append(conteudoEventoAssinado);
        builder.append("</evento>");
        builder.append("</eventos>");
        builder.append("</envioLoteEventos>");
        builder.append("</Reinf>");

        String evento = builder.toString();

        evento = evento.replace("${id}", id);
        evento = evento.replace("${nrInscEmpregador}", nrInscEmpregador);
        evento = evento.replace("${tpInscEmpregador}", tpInscEmpregador);
        return evento;
    }

    private String extractSignedEventContent(String xmlAssinado) {
        // Extrair o conteúdo do evento assinado, mantendo a assinatura
        String content = xmlAssinado;

        // Se contém elemento Reinf externo, extrair o conteúdo interno (incluindo a assinatura)
        if (content.contains("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/")) {
            int startIndex = content.indexOf(">", content.indexOf("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/")) + 1;
            int endIndex = content.lastIndexOf("</Reinf>");
            if (startIndex > 0 && endIndex > startIndex) {
                content = content.substring(startIndex, endIndex).trim();
            }
        }

        return content;
    }
}
