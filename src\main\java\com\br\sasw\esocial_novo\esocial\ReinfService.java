package com.br.sasw.esocial_novo.esocial;

import com.br.sasw.esocial_novo.service.HttpClientService;
import com.br.sasw.esocial_novo.util.XmlSigner;
import com.br.sasw.esocial_novo.util.CarregarCertificados;
import com.br.sasw.esocial_novo.util.Certificado;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.StringWriter;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Collections;

@Service
@RequiredArgsConstructor
public class ReinfService {

    private final HttpClientService httpClientService;

    /**
     * Processa e envia um evento REINF versão 2.1.2
     *
     * Estratégia correta:
     * 1. Construir envelope completo com evento SEM assinatura
     * 2. Assinar APENAS o evento dentro do envelope já montado
     * 3. Isso mantém a assinatura válida no contexto final
     */
    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        // 1. Construir envelope completo com evento SEM assinatura
        String envelopeCompleto = buildEnvelopeCompletoSemAssinatura(xml, id, nrInscEmpregador, tpInscEmpregador);

        // 2. Assinar APENAS o evento dentro do envelope já montado
        String envelopeAssinado = signEventoNoEnvelope(envelopeCompleto, id, empresa);

        return envelopeAssinado;
    }

    /**
     * Constrói o envelope completo com o evento SEM assinatura
     * O evento será assinado depois, dentro do contexto do envelope
     */
    private String buildEnvelopeCompletoSemAssinatura(String xml, String id, String nrInscEmpregador, String tpInscEmpregador) {
        // Remove declaração XML do evento se existir
        String eventoLimpo = xml;
        if (eventoLimpo.startsWith("<?xml")) {
            int xmlDeclEnd = eventoLimpo.indexOf("?>");
            if (xmlDeclEnd > 0) {
                eventoLimpo = eventoLimpo.substring(xmlDeclEnd + 2).trim();
            }
        }

        StringBuilder envelope = new StringBuilder();
        envelope.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        envelope.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        envelope.append("<envioLoteEventos>");
        envelope.append("<ideContribuinte>");
        envelope.append("<tpInsc>").append(tpInscEmpregador).append("</tpInsc>");
        envelope.append("<nrInsc>").append(nrInscEmpregador).append("</nrInsc>");
        envelope.append("</ideContribuinte>");
        envelope.append("<eventos>");
        envelope.append("<evento Id=\"").append(id).append("\">");
        envelope.append(eventoLimpo);
        envelope.append("</evento>");
        envelope.append("</eventos>");
        envelope.append("</envioLoteEventos>");
        envelope.append("</Reinf>");

        return envelope.toString();
    }

    /**
     * Assina APENAS o evento dentro do envelope já montado
     * Isso garante que a assinatura seja válida no contexto final
     */
    private String signEventoNoEnvelope(String envelopeCompleto, String eventId, String empresa) {
        try {
            Certificado certificate = CarregarCertificados.buscaCertificadoPorChaveEmpresa(empresa);
            String caminhoCertificado = certificate.getLocal();
            String senhaCertificado = certificate.getSenha();
            String alias = null;

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new FileInputStream(caminhoCertificado), senhaCertificado.toCharArray());

            for (var e = keyStore.aliases(); e.hasMoreElements(); ) {
                alias = e.nextElement();
            }

            PrivateKey chavePrivada = (PrivateKey) keyStore.getKey(alias, senhaCertificado.toCharArray());
            X509Certificate certificado = (X509Certificate) keyStore.getCertificate(alias);

            // Carrega o envelope completo
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document doc = dbf.newDocumentBuilder().parse(new InputSource(new ByteArrayInputStream(envelopeCompleto.getBytes("utf-8"))));

            // Encontra o elemento do evento dentro do envelope que tem o ID
            org.w3c.dom.Element elementWithId = findElementWithIdInDocument(doc, eventId);
            if (elementWithId == null) {
                throw new RuntimeException("Elemento com ID '" + eventId + "' não encontrado no envelope");
            }

            // Configura o atributo id como ID para o resolvedor de referências
            elementWithId.setIdAttribute("id", true);

            // Cria a fábrica de assinatura
            XMLSignatureFactory sigFactory = XMLSignatureFactory.getInstance("DOM");

            // Cria a referência com ID específico do evento
            Reference ref = sigFactory.newReference("#" + eventId,
                    sigFactory.newDigestMethod(DigestMethod.SHA256, null),
                    Arrays.asList(
                            sigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                            sigFactory.newTransform(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null)
                    ),
                    null,
                    null
            );

            // Define as informações da assinatura
            SignedInfo signedInfo = sigFactory.newSignedInfo(
                    sigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
                    sigFactory.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
                    Collections.singletonList(ref)
            );

            // Cria a KeyInfo com o certificado do usuário final (EndCertOnly)
            KeyInfoFactory kif = sigFactory.getKeyInfoFactory();
            X509Data x509Data = kif.newX509Data(Collections.singletonList(certificado));
            KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(x509Data));

            // Encontra o elemento Reinf dentro do evento para adicionar a assinatura
            org.w3c.dom.Element reinfElement = findReinfElementInEvent(doc, eventId);
            if (reinfElement == null) {
                throw new RuntimeException("Elemento Reinf não encontrado dentro do evento");
            }

            // Define o local da assinatura (dentro do elemento Reinf do evento)
            DOMSignContext dsc = new DOMSignContext(chavePrivada, reinfElement);

            // Cria e aplica a assinatura
            XMLSignature signature = sigFactory.newXMLSignature(signedInfo, keyInfo);
            signature.sign(dsc);

            // Salva o resultado
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "no");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "1");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.METHOD, "xml");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.STANDALONE, "no");
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "no");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.toString();
        } catch (Exception e) {
            throw new RuntimeException("Erro ao assinar evento no envelope: " + e.getMessage(), e);
        }
    }

    /**
     * Encontra o elemento com o ID especificado no documento
     */
    private org.w3c.dom.Element findElementWithIdInDocument(Document doc, String id) {
        org.w3c.dom.NodeList nodeList = doc.getElementsByTagName("*");
        for (int i = 0; i < nodeList.getLength(); i++) {
            org.w3c.dom.Node node = nodeList.item(i);
            if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
                org.w3c.dom.Element element = (org.w3c.dom.Element) node;
                String elementId = element.getAttribute("id");
                if (id.equals(elementId)) {
                    return element;
                }
            }
        }
        return null;
    }

    /**
     * Encontra o elemento Reinf dentro do evento para adicionar a assinatura
     */
    private org.w3c.dom.Element findReinfElementInEvent(Document doc, String eventId) {
        // Primeiro encontra o evento
        org.w3c.dom.NodeList eventos = doc.getElementsByTagName("evento");
        for (int i = 0; i < eventos.getLength(); i++) {
            org.w3c.dom.Element evento = (org.w3c.dom.Element) eventos.item(i);
            if (eventId.equals(evento.getAttribute("Id"))) {
                // Dentro do evento, procura o elemento Reinf
                org.w3c.dom.NodeList children = evento.getChildNodes();
                for (int j = 0; j < children.getLength(); j++) {
                    org.w3c.dom.Node child = children.item(j);
                    if (child.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
                        org.w3c.dom.Element childElement = (org.w3c.dom.Element) child;
                        if ("Reinf".equals(childElement.getLocalName())) {
                            return childElement;
                        }
                    }
                }
            }
        }
        return null;
    }
}
