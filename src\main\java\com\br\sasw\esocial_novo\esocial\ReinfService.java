package com.br.sasw.esocial_novo.esocial;

import com.br.sasw.esocial_novo.service.HttpClientService;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReinfService {

    private final HttpClientService httpClientService;

    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        // Para REINF, NÃO usamos assinatura digital - apenas construímos o envelope sem assinatura
        String eventoCompleto = buildEventWithoutSignature(xml, id, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor,tpInscTransmissor);

        return eventoCompleto;
    }

    private String buildEventWithoutSignature(String xmlEvento, String id, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){
        // Extrair apenas o conteúdo do evento, removendo qualquer elemento Reinf externo
        String conteudoEvento = extractEventContentWithoutSignature(xmlEvento);

        StringBuilder builder = new StringBuilder();
        builder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        builder.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        builder.append("<envioLoteEventos>");
        builder.append("<ideContribuinte>");
        builder.append("<tpInsc>${tpInscEmpregador}</tpInsc>");
        builder.append("<nrInsc>${nrInscEmpregador}</nrInsc>");
        builder.append("</ideContribuinte>");
        builder.append("<eventos>");
        builder.append("<evento Id=\"${id}\">");
        builder.append(conteudoEvento);
        builder.append("</evento>");
        builder.append("</eventos>");
        builder.append("</envioLoteEventos>");
        builder.append("</Reinf>");

        String evento = builder.toString();

        evento = evento.replace("${id}", id);
        evento = evento.replace("${nrInscEmpregador}", nrInscEmpregador);
        evento = evento.replace("${tpInscEmpregador}", tpInscEmpregador);
        return evento;
    }

    private String extractEventContentWithoutSignature(String xml) {
        // Extrair o conteúdo do evento preservando seu namespace original
        String content = xml;

        // Verificar diferentes namespaces possíveis para eventos REINF
        String[] possibleNamespaces = {
            "<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtServPrest/",
            "<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtServTom/",
            "<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/",
            "<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/"
        };

        String originalNamespace = null;

        // Se contém elemento Reinf externo, extrair o conteúdo interno e preservar o namespace
        for (String namespace : possibleNamespaces) {
            if (content.contains(namespace)) {
                // Capturar o namespace completo
                int namespaceStart = content.indexOf(namespace);
                int namespaceEnd = content.indexOf("\"", namespaceStart + namespace.length());
                if (namespaceEnd > namespaceStart) {
                    originalNamespace = content.substring(namespaceStart + 15, namespaceEnd); // Remove "<Reinf xmlns=\""
                }

                int startIndex = content.indexOf(">", content.indexOf(namespace)) + 1;
                int endIndex = content.lastIndexOf("</Reinf>");
                if (startIndex > 0 && endIndex > startIndex) {
                    content = content.substring(startIndex, endIndex).trim();
                    break;
                }
            }
        }

        // Remover qualquer elemento Signature se existir
        if (content.contains("<Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\">")) {
            int signatureStart = content.indexOf("<Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\">");
            int signatureEnd = content.indexOf("</Signature>") + "</Signature>".length();
            if (signatureStart >= 0 && signatureEnd > signatureStart) {
                content = content.substring(0, signatureStart) + content.substring(signatureEnd);
                content = content.trim();
            }
        }

        // Se temos um namespace original e o conteúdo não tem namespace, adicionar o namespace ao elemento raiz
        if (originalNamespace != null && !content.contains("xmlns=")) {
            // Encontrar o primeiro elemento e adicionar o namespace
            int firstElementEnd = content.indexOf(">");
            if (firstElementEnd > 0) {
                String elementTag = content.substring(0, firstElementEnd);
                String restOfContent = content.substring(firstElementEnd);
                content = elementTag + " xmlns=\"" + originalNamespace + "\"" + restOfContent;
            }
        }

        return content;
    }
}
