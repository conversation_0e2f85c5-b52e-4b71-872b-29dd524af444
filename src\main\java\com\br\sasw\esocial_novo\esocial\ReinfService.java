package com.br.sasw.esocial_novo.esocial;

import com.br.sasw.esocial_novo.service.HttpClientService;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReinfService {

    private final HttpClientService httpClientService;

    /**
     * Processa e envia um evento REINF versão 2.1.2
     *
     * Estratégia corrigida baseada no erro MS0030:
     * 1. Assinar o evento individual (obrigatório)
     * 2. Construir envelope de lote com evento assinado
     * 3. Assinar o envelope completo
     */
    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        // 1. Assinar o evento individual (obrigatório conforme erro MS0030)
        String eventoAssinado = XmlSigner.sign(xml, empresa);

        // 2. Extrair o evento assinado do XML
        String eventoLimpo = extractEventFromXml(eventoAssinado);

        // 3. Construir envelope de lote com evento assinado
        String envelopeLote = buildEnvelopeLote(eventoLimpo, id, nrInscEmpregador, tpInscEmpregador);

        // 4. Assinar o envelope completo
        String xmlFinal = XmlSigner.sign(envelopeLote, empresa);

        return xmlFinal;
    }

    /**
     * Extrai o evento do XML de entrada, preservando assinatura se existir
     */
    private String extractEventFromXml(String xml) {
        String content = xml.trim();

        // Remover declaração XML se existir
        if (content.startsWith("<?xml")) {
            int xmlDeclEnd = content.indexOf("?>");
            if (xmlDeclEnd > 0) {
                content = content.substring(xmlDeclEnd + 2).trim();
            }
        }

        // Se o XML está envolvido em um elemento Reinf externo, extrair o conteúdo interno
        // Preservando a assinatura se existir
        if (content.startsWith("<Reinf xmlns=")) {
            int startTag = content.indexOf(">");
            int endTag = content.lastIndexOf("</Reinf>");

            if (startTag > 0 && endTag > startTag) {
                content = content.substring(startTag + 1, endTag).trim();
            }
        }

        return content;
    }



    /**
     * Constrói o envelope de lote de eventos conforme especificação REINF 2.1.2
     */
    private String buildEnvelopeLote(String evento, String id, String nrInscEmpregador, String tpInscEmpregador) {
        StringBuilder envelope = new StringBuilder();

        envelope.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        envelope.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        envelope.append("<envioLoteEventos>");
        envelope.append("<ideContribuinte>");
        envelope.append("<tpInsc>").append(tpInscEmpregador).append("</tpInsc>");
        envelope.append("<nrInsc>").append(nrInscEmpregador).append("</nrInsc>");
        envelope.append("</ideContribuinte>");
        envelope.append("<eventos>");
        envelope.append("<evento Id=\"").append(id).append("\">");
        envelope.append(evento);
        envelope.append("</evento>");
        envelope.append("</eventos>");
        envelope.append("</envioLoteEventos>");
        envelope.append("</Reinf>");

        return envelope.toString();
    }
}
